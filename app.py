import tkinter as tk
from tkinter import ttk, messagebox
from student import Student

class StudentManagementApp:
    def __init__(self, root):
        self.root = root
        self.root.title("Student Management")
        self.student = Student()
        self.setup_ui()
        self.refresh_list()

    def setup_ui(self):
        # Input fields
        tk.Label(self.root, text="Name:").grid(row=0, column=0)
        self.name_var = tk.StringVar()
        tk.Entry(self.root, textvariable=self.name_var).grid(row=0, column=1)

        tk.Label(self.root, text="Email:").grid(row=0, column=2)
        self.email_var = tk.StringVar()
        tk.Entry(self.root, textvariable=self.email_var).grid(row=0, column=3)

        tk.Label(self.root, text="Age:").grid(row=1, column=0)
        self.age_var = tk.StringVar()
        tk.Entry(self.root, textvariable=self.age_var).grid(row=1, column=1)

        # Buttons
        tk.Button(self.root, text="Add", command=self.add_student).grid(row=2, column=0)
        tk.Button(self.root, text="Update", command=self.update_student).grid(row=2, column=1)
        tk.Button(self.root, text="Delete", command=self.delete_student).grid(row=2, column=2)
        tk.Button(self.root, text="Clear", command=self.clear_fields).grid(row=2, column=3)

        # Students list
        columns = ("ID", "Name", "Email", "Age", "Date")
        self.tree = ttk.Treeview(self.root, columns=columns, show="headings")
        for col in columns:
            self.tree.heading(col, text=col)
        self.tree.grid(row=3, column=0, columnspan=4)
        self.tree.bind("<<TreeviewSelect>>", self.on_select)

    def add_student(self):
        if self.validate_input():
            success, msg = self.student.create(self.name_var.get(), self.email_var.get(), int(self.age_var.get()))
            messagebox.showinfo("Result", msg)
            if success:
                self.clear_fields()
                self.refresh_list()

    def update_student(self):
        selected = self.tree.selection()
        if not selected:
            messagebox.showwarning("Warning", "Select a student first")
            return
        if self.validate_input():
            student_id = self.tree.item(selected[0])['values'][0]
            success, msg = self.student.update(student_id, self.name_var.get(), self.email_var.get(), int(self.age_var.get()))
            messagebox.showinfo("Result", msg)
            if success:
                self.clear_fields()
                self.refresh_list()

    def delete_student(self):
        selected = self.tree.selection()
        if not selected:
            messagebox.showwarning("Warning", "Select a student first")
            return
        if messagebox.askyesno("Confirm", "Delete this student?"):
            student_id = self.tree.item(selected[0])['values'][0]
            success, msg = self.student.delete(student_id)
            messagebox.showinfo("Result", msg)
            if success:
                self.clear_fields()
                self.refresh_list()

    def on_select(self, event):
        selected = self.tree.selection()
        if selected:
            values = self.tree.item(selected[0])['values']
            self.name_var.set(values[1])
            self.email_var.set(values[2])
            self.age_var.set(values[3])

    def clear_fields(self):
        self.name_var.set("")
        self.email_var.set("")
        self.age_var.set("")

    def validate_input(self):
        if not self.name_var.get().strip() or not self.email_var.get().strip():
            messagebox.showerror("Error", "Name and Email required")
            return False
        try:
            age = int(self.age_var.get())
            if age <= 0: raise ValueError
        except:
            messagebox.showerror("Error", "Age must be positive number")
            return False
        return True

    def refresh_list(self):
        for item in self.tree.get_children():
            self.tree.delete(item)
        for student in self.student.read_all():
            self.tree.insert("", tk.END, values=student)

if __name__ == "__main__":
    root = tk.Tk()
    app = StudentManagementApp(root)
    root.mainloop()
