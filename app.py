import tkinter as tk
from tkinter import ttk, messagebox
from student import Student

class StudentManagementApp:
    def __init__(self, root):
        self.root = root
        self.root.title("Student Management System")
        self.root.geometry("800x600")
        self.student = Student()
        self.setup_ui()
        self.refresh_list()
    
    def setup_ui(self):
        # Main frame
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Input frame
        input_frame = ttk.LabelFrame(main_frame, text="Student Information", padding="10")
        input_frame.grid(row=0, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        
        # Input fields
        ttk.Label(input_frame, text="Name:").grid(row=0, column=0, sticky=tk.W, padx=(0, 5))
        self.name_var = tk.StringVar()
        ttk.Entry(input_frame, textvariable=self.name_var, width=20).grid(row=0, column=1, padx=(0, 10))
        
        ttk.Label(input_frame, text="Email:").grid(row=0, column=2, sticky=tk.W, padx=(0, 5))
        self.email_var = tk.StringVar()
        ttk.Entry(input_frame, textvariable=self.email_var, width=25).grid(row=0, column=3, padx=(0, 10))
        
        ttk.Label(input_frame, text="Age:").grid(row=1, column=0, sticky=tk.W, padx=(0, 5), pady=(5, 0))
        self.age_var = tk.StringVar()
        ttk.Entry(input_frame, textvariable=self.age_var, width=10).grid(row=1, column=1, pady=(5, 0))
        
        # Buttons frame
        btn_frame = ttk.Frame(input_frame)
        btn_frame.grid(row=2, column=0, columnspan=4, pady=(10, 0))
        
        ttk.Button(btn_frame, text="Add", command=self.add_student).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(btn_frame, text="Update", command=self.update_student).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(btn_frame, text="Delete", command=self.delete_student).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(btn_frame, text="Clear", command=self.clear_fields).pack(side=tk.LEFT)
        
        # Students list
        list_frame = ttk.LabelFrame(main_frame, text="Students List", padding="10")
        list_frame.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Treeview
        columns = ("ID", "Name", "Email", "Age", "Enrollment Date")
        self.tree = ttk.Treeview(list_frame, columns=columns, show="headings", height=15)
        
        for col in columns:
            self.tree.heading(col, text=col)
            self.tree.column(col, width=120)
        
        scrollbar = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=self.tree.yview)
        self.tree.configure(yscrollcommand=scrollbar.set)
        
        self.tree.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        
        self.tree.bind("<<TreeviewSelect>>", self.on_select)
        
        # Configure grid weights
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(0, weight=1)
        main_frame.rowconfigure(1, weight=1)
        list_frame.columnconfigure(0, weight=1)
        list_frame.rowconfigure(0, weight=1)
    
    def add_student(self):
        if self.validate_input():
            success, message = self.student.create(
                self.name_var.get(),
                self.email_var.get(),
                int(self.age_var.get())
            )
            if success:
                messagebox.showinfo("Success", message)
                self.clear_fields()
                self.refresh_list()
            else:
                messagebox.showerror("Error", message)
    
    def update_student(self):
        selected = self.tree.selection()
        if not selected:
            messagebox.showwarning("Warning", "Please select a student to update")
            return
        
        if self.validate_input():
            item = self.tree.item(selected[0])
            student_id = item['values'][0]
            
            success, message = self.student.update(
                student_id,
                self.name_var.get(),
                self.email_var.get(),
                int(self.age_var.get())
            )
            if success:
                messagebox.showinfo("Success", message)
                self.clear_fields()
                self.refresh_list()
            else:
                messagebox.showerror("Error", message)
    
    def delete_student(self):
        selected = self.tree.selection()
        if not selected:
            messagebox.showwarning("Warning", "Please select a student to delete")
            return
        
        if messagebox.askyesno("Confirm", "Are you sure you want to delete this student?"):
            item = self.tree.item(selected[0])
            student_id = item['values'][0]
            
            success, message = self.student.delete(student_id)
            if success:
                messagebox.showinfo("Success", message)
                self.clear_fields()
                self.refresh_list()
            else:
                messagebox.showerror("Error", message)
    
    def on_select(self, event):
        selected = self.tree.selection()
        if selected:
            item = self.tree.item(selected[0])
            values = item['values']
            self.name_var.set(values[1])
            self.email_var.set(values[2])
            self.age_var.set(values[3])
    
    def clear_fields(self):
        self.name_var.set("")
        self.email_var.set("")
        self.age_var.set("")
        self.tree.selection_remove(self.tree.selection())
    
    def validate_input(self):
        if not self.name_var.get().strip():
            messagebox.showerror("Error", "Name is required")
            return False
        if not self.email_var.get().strip():
            messagebox.showerror("Error", "Email is required")
            return False
        try:
            age = int(self.age_var.get())
            if age <= 0:
                raise ValueError
        except ValueError:
            messagebox.showerror("Error", "Age must be a positive number")
            return False
        return True
    
    def refresh_list(self):
        for item in self.tree.get_children():
            self.tree.delete(item)
        
        students = self.student.read_all()
        for student in students:
            self.tree.insert("", tk.END, values=student)

if __name__ == "__main__":
    root = tk.Tk()
    app = StudentManagementApp(root)
    root.mainloop()
