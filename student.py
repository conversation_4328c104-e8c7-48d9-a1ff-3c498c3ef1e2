from database import Database
from datetime import datetime

class Student:
    def __init__(self):
        self.db = Database()
    
    def create(self, name, email, age):
        """Create a new student"""
        try:
            with self.db.get_connection() as conn:
                cursor = conn.cursor()
                enrollment_date = datetime.now().strftime("%Y-%m-%d")
                cursor.execute(
                    "INSERT INTO students (name, email, age, enrollment_date) VALUES (?, ?, ?, ?)",
                    (name, email, age, enrollment_date)
                )
                conn.commit()
                return True, "Student added successfully"
        except Exception as e:
            return False, str(e)
    
    def read_all(self):
        """Get all students"""
        try:
            with self.db.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("SELECT * FROM students")
                return cursor.fetchall()
        except Exception as e:
            return []
    
    def read_by_id(self, student_id):
        """Get student by ID"""
        try:
            with self.db.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("SELECT * FROM students WHERE id = ?", (student_id,))
                return cursor.fetchone()
        except Exception as e:
            return None
    
    def update(self, student_id, name, email, age):
        """Update student information"""
        try:
            with self.db.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute(
                    "UPDATE students SET name = ?, email = ?, age = ? WHERE id = ?",
                    (name, email, age, student_id)
                )
                conn.commit()
                return True, "Student updated successfully"
        except Exception as e:
            return False, str(e)
    
    def delete(self, student_id):
        """Delete a student"""
        try:
            with self.db.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("DELETE FROM students WHERE id = ?", (student_id,))
                conn.commit()
                return True, "Student deleted successfully"
        except Exception as e:
            return False, str(e)
